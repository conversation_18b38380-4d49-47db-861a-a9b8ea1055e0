\relax 
\providecommand \babel@aux [2]{\global \let \babel@toc \@gobbletwo }
\@nameuse{bbl@beforestart}
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\bibstyle{naturemag-doi}
\bibdata{sample}
\bibcite{Hao:gidmaps:2014}{1}
\citation{Hao:gidmaps:2014}
\babel@aux{english}{}
\@writefile{toc}{\contentsline {section}{\hspace  *{-\tocsep }References}{1}{Doc-Start}\protected@file@percent }
\ttl@finishall
\newlabel{LastPage}{{}{1}{}{page.1}{}}
\gdef\lastpage@lastpage{1}
\gdef\lastpage@lastpageHy{1}
\gdef \@abspage@last{1}
