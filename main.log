This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.6.12)  3 AUG 2025 17:24
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Desktop/Research/已投稿/NC肠道菌群中介/latex/main.tex
(/Users/<USER>/Desktop/Research/已投稿/NC肠道菌群中介/latex/main.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(./wlscirep.cls
Document Class: wlscirep 08/08/2020, v1.4
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
) (/usr/local/texlive/2025/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2025/02/14 v25.4 The multilingual framework for pdfLaTeX, LuaLaTeX and XeLaTeX
\babel@savecnt=\count196
\U@D=\dimen141
\l@unhyphenated=\language90
 (/usr/local/texlive/2025/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count197
 (/usr/local/texlive/2025/texmf-dist/tex/generic/babel-english/english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language22). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language22). Reported on input line 108.
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/babel/locale/en/babel-english.tex
Package babel Info: Importing font and identification data for english
(babel)             from babel-en.ini. Reported on input line 11.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count198
\calc@Bcount=\count199
\calc@Adimen=\dimen142
\calc@Bdimen=\dimen143
\calc@Askip=\skip49
\calc@Bskip=\skip50
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count266
\calc@Cskip=\skip51
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/fleqn.clo
File: fleqn.clo 2016/12/29 v1.2b Standard LaTeX option (flush left equations)
\mathindent=\skip52
Applying: [2015/01/01] Make \[ robust on input line 50.
LaTeX Info: Redefining \[ on input line 51.
Already applied: [0000/00/00] Make \[ robust on input line 62.
Applying: [2015/01/01] Make \] robust on input line 74.
LaTeX Info: Redefining \] on input line 75.
Already applied: [0000/00/00] Make \] robust on input line 83.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count267
\c@section=\count268
\c@subsection=\count269
\c@subsubsection=\count270
\c@paragraph=\count271
\c@subparagraph=\count272
\c@figure=\count273
\c@table=\count274
\abovecaptionskip=\skip53
\belowcaptionskip=\skip54
\bibindent=\dimen144
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip55

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen145
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen146
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count275
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count276
\leftroot@=\count277
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count278
\DOTSCASE@=\count279
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen147
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count280
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count281
\dotsspace@=\muskip17
\c@parentequation=\count282
\dspbrk@lvl=\count283
\tag@help=\toks20
\row@=\count284
\column@=\count285
\maxfields@=\count286
\andhelp@=\toks21
\eqnshift@=\dimen148
\alignsep@=\dimen149
\tagshift@=\dimen150
\tagwidth@=\dimen151
\totwidth@=\dimen152
\lineht@=\dimen153
\@envbody=\toks22
\multlinegap=\skip56
\multlinetaggap=\skip57
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/mathptmx.sty
Package: mathptmx 2020/03/25 PSNFSS-v9.3 Times w/ Math, improved (SPQR, WaS) 
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ztmcm/m/n on input line 28.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ztmcm/m/it on input line 29.
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/ztmcm/m/n on input line 30.
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/ztmcm/m/n on input line 31.
\symbold=\mathgroup6
\symitalic=\mathgroup7
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/ptm/bx/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ptm/bx/n on input line 34.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/ptm/m/it on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/ptm/m/it on input line 35.
LaTeX Info: Redefining \hbar on input line 50.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks24
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/courier.sty
Package: courier 2020/03/25 PSNFSS-v9.3 (WaS) 
) (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifpdf.sty
Package: ifpdf 2019/10/25 v3.4 ifpdf legacy package. Use iftex instead.
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen154
\Gin@req@width=\dimen155
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen156
\lightrulewidth=\dimen157
\cmidrulewidth=\dimen158
\belowrulesep=\dimen159
\belowbottomsep=\dimen160
\aboverulesep=\dimen161
\abovetopsep=\dimen162
\cmidrulesep=\dimen163
\cmidrulekern=\dimen164
\defaultaddspace=\dimen165
\@cmidla=\count287
\@cmidlb=\count288
\@aboverulesep=\dimen166
\@belowrulesep=\dimen167
\@thisruleclass=\count289
\@lastruleclass=\count290
\@thisrulewidth=\dimen168
) (/usr/local/texlive/2025/texmf-dist/tex/latex/preprint/authblk.sty
Package: authblk 2001/02/27 1.3 (PWD)
\affilsep=\skip58
\@affilsep=\skip59
\c@Maxaffil=\count291
\c@authors=\count292
\c@affil=\count293
) (/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count294
\Gm@cntv=\count295
\c@Gm@tempcnt=\count296
\Gm@bindingoffset=\dimen169
\Gm@wd@mp=\dimen170
\Gm@odd@mp=\dimen171
\Gm@even@mp=\dimen172
\Gm@layoutwidth=\dimen173
\Gm@layoutheight=\dimen174
\Gm@layouthoffset=\dimen175
\Gm@layoutvoffset=\dimen176
\Gm@dimlist=\toks25
) (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen177
\captionmargin=\dimen178
\caption@leftmargin=\dimen179
\caption@rightmargin=\dimen180
\caption@width=\dimen181
\caption@indent=\dimen182
\caption@parindent=\dimen183
\caption@hangindent=\dimen184
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count297
\c@continuedfloat=\count298
) (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (/usr/local/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count299
) (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count300
) (/usr/local/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen185
\Hy@linkcounter=\count301
\Hy@pagecounter=\count302
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/usr/local/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count303
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count304
 (/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen186
 (/usr/local/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count305
\Field@Width=\dimen187
\Fld@charsize=\dimen188
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring ON on input line 6060.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count306
\c@Item=\count307
\c@Hfootnote=\count308
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX
 (/usr/local/texlive/2025/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count309
\c@bookmark@seq@number=\count310
 (/usr/local/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip60
) (/usr/local/texlive/2025/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
LaTeX Info: Redefining \cite on input line 441.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers
\f@nch@headwidth=\skip61
\f@nch@offset@elh=\skip62
\f@nch@offset@erh=\skip63
\f@nch@offset@olh=\skip64
\f@nch@offset@orh=\skip65
\f@nch@offset@elf=\skip66
\f@nch@offset@erf=\skip67
\f@nch@offset@olf=\skip68
\f@nch@offset@orf=\skip69
\f@nch@height=\skip70
\f@nch@footalignment=\skip71
\f@nch@widthL=\skip72
\f@nch@widthC=\skip73
\f@nch@widthR=\skip74
\@temptokenb=\toks26
) (/usr/local/texlive/2025/texmf-dist/tex/latex/lastpage/lastpage.sty
Package: lastpage 2025/01/27 v2.1e lastpage: 2.09 or 2e? (HMM)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/lastpage/lastpage2e.sty
Package: lastpage2e 2025/01/27 v2.1e Decide which 2e lastpage version to use (HMM)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/lastpage/lastpagemodern.sty
Package: lastpagemodern 2025-01-27 v2.1e Refers to last page's name (HMM; JPG)
\c@lastpagecount=\count311
) 
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box54
\beforetitleunit=\skip75
\aftertitleunit=\skip76
\ttl@plus=\dimen189
\ttl@minus=\dimen190
\ttl@toksa=\toks27
\titlewidth=\dimen191
\titlewidthlast=\dimen192
\titlewidthfirst=\dimen193
) (/usr/local/texlive/2025/texmf-dist/tex/latex/titlesec/titletoc.sty
Package: titletoc 2025/01/04 v2.17 TOC entries
\ttl@leftsep=\dimen194
) (/usr/local/texlive/2025/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip77
\enit@outerparindent=\dimen195
\enit@toks=\toks28
\enit@inbox=\box55
\enit@count@id=\count312
\enitdp@description=\count313
)
\tocsep=\skip78
 (/usr/local/texlive/2025/texmf-dist/tex/latex/lipsum/lipsum.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count314
\l__pdf_internal_box=\box56
))
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
)
Package: lipsum 2021-09-20 v2.7 150 paragraphs of Lorem Ipsum dummy text
\g__lipsum_par_int=\count315
\l__lipsum_a_int=\count316
\l__lipsum_b_int=\count317
 (/usr/local/texlive/2025/texmf-dist/tex/latex/lipsum/lipsum.ltd.tex))) (./jabbrv.sty
Package: jabbrv 2010/08/18 v0.2 Automatic Journal Title Abbreviation Package
\temp@journal@text=\toks29
\temp@journal@text@=\toks30
\c@jabbrv@strlen@count=\count318
\c@jabbrv@gobble@temp=\count319
\c@jabbrv@loop@i=\count320
\c@jabbrv@loop@max=\count321
\c@jabbrv@word@count=\count322
 (./jabbrv-ltwa-all.ldf) (./jabbrv-ltwa-en.ldf)) (/usr/local/texlive/2025/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2025/02/11 v3.2a Micro-typographical refinements (RS)
\MT@toks=\toks31
\MT@tempbox=\box57
\MT@count=\count323
LaTeX Info: Redefining \noprotrusionifhmode on input line 1087.
LaTeX Info: Redefining \leftprotrusion on input line 1088.
\MT@prot@toks=\toks32
LaTeX Info: Redefining \rightprotrusion on input line 1107.
LaTeX Info: Redefining \textls on input line 1449.
\MT@outer@kern=\dimen196
LaTeX Info: Redefining \microtypecontext on input line 2053.
LaTeX Info: Redefining \textmicrotypecontext on input line 2070.
\MT@listname@count=\count324
 (/usr/local/texlive/2025/texmf-dist/tex/latex/microtype/microtype-pdftex.def
File: microtype-pdftex.def 2025/02/11 v3.2a Definitions specific to pdftex (RS)
LaTeX Info: Redefining \lsstyle on input line 944.
LaTeX Info: Redefining \lslig on input line 944.
\MT@outer@space=\skip79
)
Package microtype Info: Loading configuration file microtype.cfg.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2025/02/11 v3.2a microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3065.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+ptm on input line 116.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/t1ptm.fd
File: t1ptm.fd 2001/06/04 font definitions for T1/ptm.
))
No file main.aux.
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 22.
LaTeX Font Info:    ... okay on input line 22.
(/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count325
\scratchdimen=\dimen197
\scratchbox=\box58
\nofMPsegments=\count326
\nofMParguments=\count327
\everyMPshowfont=\toks33
\MPscratchCnt=\count328
\MPscratchDim=\dimen198
\MPnumerator=\count329
\makeMPintoPDFobject=\count330
\everyMPtoPDFconversion=\toks34
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
 (/usr/local/texlive/2025/texmf-dist/tex/latex/grfext/grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)
)
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPEG,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: letterpaper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(56.9055pt, 500.484pt, 56.9055pt)
* v-part:(T,H,B)=(64.01869pt, 666.9326pt, 64.01869pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=500.484pt
* \textheight=666.9326pt
* \oddsidemargin=-15.36449pt
* \evensidemargin=-15.36449pt
* \topmargin=-45.2513pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=15.6491pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package caption Info: Begin \AtBeginDocument code.
Package caption Info: hyperref package is loaded.
Package caption Info: End \AtBeginDocument code.
Package hyperref Info: Link coloring ON on input line 22.
\@outlinefile=\write3
\openout3 = `main.out'.

LaTeX Info: Redefining \microtypecontext on input line 22.
Package microtype Info: Applying patch `item' on input line 22.
Package microtype Info: Applying patch `toc' on input line 22.
Package microtype Info: Applying patch `eqnum' on input line 22.
Package microtype Info: Applying patch `footnote' on input line 22.
Package microtype Info: Applying patch `verbatim' on input line 22.
LaTeX Info: Redefining \microtypesetup on input line 22.
Package microtype Info: Generating PDF output.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: Automatic font expansion enabled (level 2),
(microtype)             stretch: 20, shrink: 20, step: 1, non-selected.
Package microtype Info: Using default expansion set `alltext-nott'.
LaTeX Info: Redefining \showhyphens on input line 22.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of interword spacing.
Package microtype Info: No adjustment of character kerning.
(/usr/local/texlive/2025/texmf-dist/tex/latex/microtype/mt-ptm.cfg
File: mt-ptm.cfg 2006/04/20 v1.7 microtype config. file: Times (RS)
)
LaTeX Font Info:    Trying to load font information for T1+phv on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/t1phv.fd
File: t1phv.fd 2020/03/25 scalable font definitions for T1/phv.
)
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `phv' (encoding: T1).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.
LaTeX Font Info:    Trying to load font information for OT1+phv on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/ot1phv.fd
File: ot1phv.fd 2020/03/25 scalable font definitions for OT1/phv.
)
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `phv' (encoding: OT1).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.
LaTeX Font Info:    Trying to load font information for OT1+ztmcm on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/ot1ztmcm.fd
File: ot1ztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OT1/ztmcm.
)
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `ztmcm' (encoding: OT1).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.
LaTeX Font Info:    Trying to load font information for OML+ztmcm on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/omlztmcm.fd
File: omlztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OML/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OMS+ztmcm on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/omsztmcm.fd
File: omsztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OMS/ztmcm.
)
LaTeX Font Info:    Trying to load font information for OMX+ztmcm on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/omxztmcm.fd
File: omxztmcm.fd 2000/01/03 Fontinst v1.801 font definitions for OMX/ztmcm.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
)
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 32.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 32.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 32.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 32.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7.4> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 32.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <6> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 32.


/Users/<USER>/Desktop/Research/已投稿/NC肠道菌群中介/latex/main.tex:32: LaTeX Error: Unicode character ： (U+FF1A)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.32 
     
You may provide a definition with
\DeclareUnicodeCharacter 


/Users/<USER>/Desktop/Research/已投稿/NC肠道菌群中介/latex/main.tex:32: LaTeX Error: Unicode character ： (U+FF1A)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.32 
     
You may provide a definition with
\DeclareUnicodeCharacter 

Runaway argument?
{ 
/Users/<USER>/Desktop/Research/已投稿/NC肠道菌群中介/latex/main.tex:37: Paragraph ended before \ttl@straight@i was complete.
<to be read again> 
                   \par 
l.37 
     
I suspect you've forgotten a `}', causing me to apply this
control sequence to too much text. How can we recover?
My plan is to forget the whole thing and hope for the best.

(./main.bbl)

LaTeX Warning: Citation `Hao:gidmaps:2014' on page 1 undefined on input line 40.

LaTeX Font Info:    Trying to load font information for T1+pcr on input line 42.
(/usr/local/texlive/2025/texmf-dist/tex/latex/psnfss/t1pcr.fd
File: t1pcr.fd 2001/06/04 font definitions for T1/pcr.
)
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `pcr' (encoding: T1).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.


[1

{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/base/8r.enc}] 
enddocument/afterlastpage (AED): lastpage setting LastPage.
(./main.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.


Package rerunfilecheck Warning: File `main.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `main.out':
(rerunfilecheck)             Before: <no file>
(rerunfilecheck)             After:  8B5498C3D475DCBD9490AC89561AE48C;93.

Package lastpage Warning: Rerun to get the references right on input line 62.

 ) 
Here is how much of TeX's memory you used:
 24207 strings out of 473190
 462105 string characters out of 5715800
 911882 words of memory out of 5000000
 46989 multiletter control sequences out of 15000+600000
 621300 words of font info for 212 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 94i,5n,107p,1069b,466s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/urw/courier/ucrr8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/helvetic/uhvb8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/helvetic/uhvr8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmb8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmr8a.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on main.pdf (1 page, 70688 bytes).
PDF statistics:
 47 PDF objects out of 1000 (max. 8388607)
 31 compressed objects within 1 object stream
 3 named destinations out of 1000 (max. 500000)
 85505 words of extra memory for PDF output out of 89155 (max. 10000000)

