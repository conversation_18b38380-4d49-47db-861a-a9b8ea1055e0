This is BibTeX, Version 0.99d (TeX Live 2025)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: main.aux
The style file: naturemag-doi.bst
Database file #1: sample.bib
You've used 1 entry,
            2380 wiz_defined-function locations,
            995 strings with 9162 characters,
and the built_in function-call counts, 367 in all, are:
= -- 18
> -- 16
< -- 0
+ -- 8
- -- 4
* -- 41
:= -- 42
add.period$ -- 3
call.type$ -- 1
change.case$ -- 1
chr.to.int$ -- 0
cite$ -- 1
duplicate$ -- 28
empty$ -- 32
format.name$ -- 5
if$ -- 68
int.to.chr$ -- 0
int.to.str$ -- 1
missing$ -- 7
newline$ -- 15
num.names$ -- 1
pop$ -- 12
preamble$ -- 1
purify$ -- 0
quote$ -- 0
skip$ -- 8
stack$ -- 0
substring$ -- 0
swap$ -- 32
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 0
warning$ -- 0
while$ -- 1
width$ -- 2
write$ -- 19
